{"Bounds": "0,0,584,240", "Dock": "None", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "CenterH", "DrawBackground": true, "MinimumSize": "560,240", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "ClampMovement": true, "DrawShadow": true, "ActiveImage": null, "InactiveImage": null, "ActiveColor": "255,255,255,255", "InactiveColor": "255,191,191,191", "IsClosable": false, "InnerPanel": {"Bounds": "0,32,584,208", "Dock": "Fill", "Padding": "8,8,8,8", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "560,208", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "Children": {"_buttonsPanel": {"Bounds": "8,174,544,26", "Dock": "Bottom", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,8,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": false, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "Children": {"_createButton": {"Bounds": "0,0,120,26", "Dock": "None", "Padding": "3,3,3,3", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "Left", "DrawBackground": true, "MinimumSize": "120,24", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Center", "AutoSizeToContents": false, "FontName": "sourcesansproblack", "FontSize": 12, "TextScale": 1.0, "NormalImage": null, "HoveredImage": null, "ClickedImage": null, "DisabledImage": null, "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}, "CenterImage": false}, "_backButton": {"Bounds": "424,0,120,26", "Dock": "None", "Padding": "3,3,3,3", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "Right", "DrawBackground": true, "MinimumSize": "120,24", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Center", "AutoSizeToContents": false, "FontName": "sourcesansproblack", "FontSize": 12, "TextScale": 1.0, "NormalImage": null, "HoveredImage": null, "ClickedImage": null, "DisabledImage": null, "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}, "CenterImage": false}}}, "_propertiesPanel": {"Bounds": "312,8,264,94", "Dock": "Right", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "16,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": false, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "Children": {"_nameInput": {"Bounds": "0,0,240,24", "Dock": "Top", "Padding": "4,2,4,2", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "240,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Left, CenterV", "AutoSizeToContents": false, "FontName": "sourcesansproblack", "FontSize": 12, "TextScale": 1.0, "AddTextSound": null, "RemoveTextSound": null, "SubmitSound": null}, "_classCombobox": {"Bounds": "0,32,240,28", "Dock": "Top", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "Children": {"_label": {"Bounds": "0,4,37,20", "Dock": "Left, CenterV", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "CenterV", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": false, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "BackgroundTemplate": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Left, Top", "AutoSizeToContents": true, "FontName": "sourcesansproblack", "FontSize": 12, "TextScale": 1.0}, "_comboBox": {"Bounds": "45,0,195,28", "Dock": "CenterV, Fill", "Padding": "8,4,0,4", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "8,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "CenterV", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Left, CenterV", "AutoSizeToContents": false, "FontName": "sourcesansproblack", "FontSize": 12, "TextScale": 1.0, "NormalImage": null, "HoveredImage": null, "ClickedImage": null, "DisabledImage": null, "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}, "CenterImage": false, "MenuAbove": false, "DropDownButton": {"Bounds": "176,6,15,15", "Dock": "None", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "4,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "15,15", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": false, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null}, "OpenMenuSound": null, "CloseMenuSound": null, "HoverMenuSound": null, "ItemHoverSound": null, "SelectItemSound": null, "Children": {"_menu": {"Bounds": "0,0,10,10", "Dock": "None", "Padding": "2,2,2,2", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "100,200", "Disabled": false, "Hidden": true, "RestrictToParent": false, "MouseInputEnabled": false, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "OverflowX": "Hidden", "OverflowY": "Auto", "HorizontalScrollBar": {"Bounds": "0,0,15,15", "Dock": "Bottom", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "15,15", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "BackgroundTemplate": null, "UpOrLeftButton": {"Bounds": "0,0,15,15", "Dock": "None", "Padding": "3,3,3,3", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "15,15", "MaximumSize": "15,15", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Center", "AutoSizeToContents": false, "FontName": null, "FontSize": null, "TextScale": 1.0, "NormalImage": null, "HoveredImage": null, "ClickedImage": null, "DisabledImage": null, "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}, "CenterImage": false}, "Bar": {"Bounds": "0,0,10,10", "Dock": "None", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": true, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "NormalImage": null, "HoveredImage": null, "ClickedImage": null, "DisabledImage": null, "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}}, "DownOrRightButton": {"Bounds": "0,0,15,15", "Dock": "None", "Padding": "3,3,3,3", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "15,15", "MaximumSize": "15,15", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Center", "AutoSizeToContents": false, "FontName": null, "FontSize": null, "TextScale": 1.0, "NormalImage": null, "HoveredImage": null, "ClickedImage": null, "DisabledImage": null, "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}, "CenterImage": false}}, "VerticalScrollBar": {"Bounds": "0,0,15,15", "Dock": "Right", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "15,15", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "BackgroundTemplate": null, "UpOrLeftButton": {"Bounds": "0,0,15,15", "Dock": "None", "Padding": "3,3,3,3", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "15,15", "MaximumSize": "15,15", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Center", "AutoSizeToContents": false, "FontName": null, "FontSize": null, "TextScale": 1.0, "NormalImage": null, "HoveredImage": null, "ClickedImage": null, "DisabledImage": null, "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}, "CenterImage": false}, "Bar": {"Bounds": "0,0,10,10", "Dock": "None", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": true, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "NormalImage": null, "HoveredImage": null, "ClickedImage": null, "DisabledImage": null, "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}}, "DownOrRightButton": {"Bounds": "0,0,15,15", "Dock": "None", "Padding": "3,3,3,3", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "15,15", "MaximumSize": "15,15", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Center", "AutoSizeToContents": false, "FontName": null, "FontSize": null, "TextScale": 1.0, "NormalImage": null, "HoveredImage": null, "ClickedImage": null, "DisabledImage": null, "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}, "CenterImage": false}}, "BackgroundTemplate": null, "ItemTextColor": "", "ItemHoveredTextColor": "", "ItemFontName": null, "ItemFontSize": 0}, "_arrowIcon": {"Bounds": "176,6,15,15", "Dock": "None", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "4,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "15,15", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": false, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null}}}}}, "_genderInputPanel": {"Bounds": "0,68,264,26", "Dock": "Top", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": false, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "Children": {"_genderMaleCheckbox": {"Bounds": "0,0,62,26", "Dock": "Left, CenterV", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "Children": {"_checkbox": {"Bounds": "0,2,22,22", "Dock": "Left, CenterV", "Padding": "3,3,3,3", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,2,2,2", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "22,22", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Center", "AutoSizeToContents": false, "FontName": null, "FontSize": null, "TextScale": 1.0, "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}, "CenterImage": false, "NormalImage": null, "CheckedImage": null, "DisabledImage": null, "CheckedDisabledImage": null, "CheckedSound": null, "UncheckedSound": null}, "_label": {"Bounds": "24,1,38,24", "Dock": "CenterV, Fill", "Padding": "2,2,2,2", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "CenterV", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "BackgroundTemplate": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Left, CenterV", "AutoSizeToContents": true, "FontName": "sourcesansproblack", "FontSize": 12, "TextScale": 1.0}}}, "_genderFemaleCheckbox": {"Bounds": "183,0,81,26", "Dock": "Right, CenterV", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "Children": {"_checkbox": {"Bounds": "0,2,22,22", "Dock": "Left, CenterV", "Padding": "3,3,3,3", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,2,2,2", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "22,22", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Center", "AutoSizeToContents": false, "FontName": null, "FontSize": null, "TextScale": 1.0, "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}, "CenterImage": false, "NormalImage": null, "CheckedImage": null, "DisabledImage": null, "CheckedDisabledImage": null, "CheckedSound": null, "UncheckedSound": null}, "_label": {"Bounds": "24,1,57,24", "Dock": "CenterV, Fill", "Padding": "2,2,2,2", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "CenterV", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "BackgroundTemplate": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Left, CenterV", "AutoSizeToContents": true, "FontName": "sourcesansproblack", "FontSize": 12, "TextScale": 1.0}}}}}}}, "_previewPanel": {"Bounds": "8,8,288,158", "Dock": "Fill", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": false, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "Children": {"_previewContainer": {"Bounds": "30,0,228,158", "Dock": "Fill", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": false, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "Children": {"_preview": {"Bounds": "25,39,178,79", "Dock": "None", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "Center", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "Texture": "character_preview_background.png", "TextureNinePatchMargin": null, "HoverSound": null, "LeftMouseClickSound": null, "RightMouseClickSound": null}}}, "_prevSpriteButton": {"Bounds": "0,61,30,35", "Dock": "Left, CenterV", "Padding": "3,3,3,3", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "30,35", "MaximumSize": "30,35", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Center", "AutoSizeToContents": false, "FontName": null, "FontSize": null, "TextScale": 1.0, "NormalImage": "button.arrow_left.normal.png", "HoveredImage": "button.arrow_left.hovered.png", "ClickedImage": "button.arrow_left.active.png", "DisabledImage": "button.arrow_left.disabled.png", "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}, "CenterImage": false}, "_nextSpriteButton": {"Bounds": "258,61,30,35", "Dock": "Right, CenterV", "Padding": "3,3,3,3", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "30,35", "MaximumSize": "30,35", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Center", "AutoSizeToContents": false, "FontName": null, "FontSize": null, "TextScale": 1.0, "NormalImage": "button.arrow_right.normal.png", "HoveredImage": "button.arrow_right.hovered.png", "ClickedImage": "button.arrow_right.active.png", "DisabledImage": "button.arrow_right.disabled.png", "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}, "CenterImage": false}}}}}, "Children": {"Titlebar": {"Bounds": "0,0,560,32", "Dock": "Top", "Padding": "4,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": false, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "NormalImage": null, "HoveredImage": null, "ClickedImage": null, "DisabledImage": null, "_stateSoundNames": {}, "Children": {"_icon": {"Bounds": "0,4,24,24", "Dock": "Left", "Padding": "0,0,0,0", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,4,0,4", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "24,24", "Disabled": false, "Hidden": true, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "Texture": null, "TextureNinePatchMargin": null, "HoverSound": null, "LeftMouseClickSound": null, "RightMouseClickSound": null}, "_label": {"Bounds": "4,0,556,32", "Dock": "CenterV, Fill", "Padding": "4,4,4,4", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": false, "RestrictToParent": false, "MouseInputEnabled": false, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "BackgroundTemplate": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Left, Bottom", "AutoSizeToContents": false, "FontName": "sourcesansproblack", "FontSize": 14, "TextScale": 1.0}, "CloseButton": {"Bounds": "0,0,32,32", "Dock": "Right", "Padding": "3,3,3,3", "AlignmentEdgeDistances": "0,0,0,0", "AlignmentTransform": "0,0", "Margin": "0,0,0,0", "RenderColor": "255,255,255,255", "Alignments": "", "DrawBackground": true, "MinimumSize": "0,0", "MaximumSize": "0,0", "Disabled": false, "Hidden": true, "RestrictToParent": false, "MouseInputEnabled": true, "HideToolTip": false, "ToolTipBackground": null, "ToolTipFont": null, "TooltipTextColor": null, "TextColor": null, "HoveredTextColor": null, "ClickedTextColor": null, "DisabledTextColor": null, "TextAlign": "Center", "AutoSizeToContents": false, "FontName": null, "FontSize": null, "TextScale": 1.0, "NormalImage": null, "HoveredImage": null, "ClickedImage": null, "DisabledImage": null, "_stateSoundNames": {"Hover": "octave-tap-resonant.wav", "MouseDown": "octave-tap-warm.wav", "MouseUp": "octave-tap-warm.wav"}, "CenterImage": false}}}}}